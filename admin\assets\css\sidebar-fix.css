/* Completely hide all scrollbars */
/* For all elements */
* {
    -ms-overflow-style: none !important;  /* IE and Edge */
    scrollbar-width: none !important;  /* Firefox */
}

/* For WebKit browsers (Chrome, Safari) */
*::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Sidebar fixes - completely remove scrollbar */
.main-sidebar {
    height: 100vh !important;
    min-height: 100% !important;
    position: fixed !important;
    overflow: hidden !important;
    padding-bottom: 0 !important;
}

/* Make nav menu fit without scrollbar */
.nav-sidebar {
    overflow: hidden !important;
}

/* Adjust sidebar height to fit content */
.sidebar-mini .main-sidebar,
.sidebar-mini-md .main-sidebar,
.sidebar-mini-xs .main-sidebar {
    overflow: hidden !important;
}

.sidebar {
    padding-bottom: 0 !important;
    overflow: hidden !important;
}

/* Adjust nav items to fit without scrollbar - compact design */
.nav-sidebar .nav-item {
    margin-bottom: 0.1rem !important;
}

/* Adjust padding for compact design */
.nav-sidebar .nav-link {
    padding: 0.5rem 0.8rem !important;
    font-size: 0.95rem !important;
}

/* Compact treeview items */
.nav-treeview .nav-link {
    padding: 0.4rem 0.8rem 0.4rem 2rem !important;
    font-size: 0.9rem !important;
}

/* Compact headers */
.nav-header {
    padding: 0.5rem 0.8rem 0.3rem 0.8rem !important;
    font-size: 1rem !important;
    margin-bottom: 0.3rem !important;
}

/* Fix content wrapper to adjust for sidebar */
.content-wrapper {
    min-height: calc(100vh - 70px) !important;
    overflow: hidden !important;
}

/* Fix footer position */
.main-footer {
    margin-left: 250px;
}

@media (max-width: 991.98px) {
    .main-footer {
        margin-left: 0;
    }
}

/* Fix for sidebar collapse */
body.sidebar-collapse .main-footer {
    margin-left: 4.6rem;
}

@media (max-width: 991.98px) {
    body.sidebar-collapse .main-footer {
        margin-left: 0;
    }
}
